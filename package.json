{"name": "yba-app", "private": true, "version": "1.0.0", "description": "zmp-blank-templates", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"start": "zmp start", "deploy": "zmp deploy", "build:css": "postcss src/css/tailwind.css -o src/css/styles.css"}, "dependencies": {"@vitejs/plugin-react": "^1.3.2", "copy-to-clipboard": "^3.3.3", "embla-carousel-react": "^8.1.6", "html2canvas": "^1.4.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-qr-reader": "^3.0.0-beta-1", "react-qr-scanner": "^1.0.0-alpha.11", "react-router-dom": "^6.23.0", "recoil": "^0.7.7", "swiper": "^11.1.15", "zmp-qrcode": "^3.0.0", "zmp-sdk": "2.39.5", "zmp-ui": "latest"}, "devDependencies": {"autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "postcss": "^8.4.38", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "sass": "^1.76.0", "tailwindcss": "^3.4.3", "vite": "^2.6.14"}}