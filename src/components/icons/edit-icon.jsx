const EditIcon = ({ customClass }) => {
    return (
        <svg
            width={16}
            height={16}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={customClass}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.7588 1.6001C12.6035 1.6001 12.4497 1.63069 12.3062 1.69013C12.1627 1.74957 12.0324 1.83668 11.9225 1.94651L10.9935 2.88543C10.8387 3.04188 10.8394 3.29399 10.995 3.44961L12.5505 5.00516C12.7062 5.16079 12.9583 5.16146 13.1147 5.00666L14.0521 4.0791L14.0536 4.07761C14.1635 3.96779 14.2506 3.83741 14.31 3.69391C14.3695 3.55042 14.4 3.39662 14.4 3.24131C14.4 3.08599 14.3695 2.9322 14.31 2.7887C14.2506 2.64521 14.1635 2.51483 14.0536 2.405L13.5951 1.94651C13.4853 1.83668 13.3549 1.74957 13.2114 1.69013C13.068 1.63069 12.9142 1.6001 12.7588 1.6001ZM10.394 4.05063C10.2379 3.8945 9.98476 3.89441 9.82852 4.05042L3.29672 10.5724C3.11991 10.7492 2.97847 10.9581 2.87997 11.1879L1.97884 13.2906C1.93503 13.3928 1.92285 13.5058 1.94388 13.615C1.96491 13.7243 2.01819 13.8247 2.09684 13.9033C2.1755 13.982 2.27592 14.0353 2.38514 14.0563C2.49436 14.0773 2.6074 14.0651 2.70963 14.0213L4.81228 13.1202C5.0421 13.0217 5.25123 12.88 5.42804 12.7032L11.9498 6.17166C12.1058 6.01542 12.1057 5.76231 11.9496 5.60619L10.394 4.05063Z"
                fill="#006EFF"
            />
        </svg>
    );
};

export default EditIcon;
