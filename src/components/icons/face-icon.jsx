const Icon = ({ customClass, color }) => {
    return (
        <svg
            width={56}
            height={56}
            viewBox="0 0 56 56"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M21.0437 23.3773C21.0437 24.6901 19.9794 25.7544 18.6666 25.7544C17.3538 25.7544 16.2895 24.6901 16.2895 23.3773C16.2895 22.0644 17.3538 21.0002 18.6666 21.0002C19.9794 21.0002 21.0437 22.0644 21.0437 23.3773Z"
                fill="#F50000"
            />
            <path
                d="M39.7104 23.3773C39.7104 24.6901 38.6461 25.7544 37.3333 25.7544C36.0204 25.7544 34.9562 24.6901 34.9562 23.3773C34.9562 22.0644 36.0204 21.0002 37.3333 21.0002C38.6461 21.0002 39.7104 22.0644 39.7104 23.3773Z"
                fill="#F50000"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M28 48.8837C39.5335 48.8837 48.8833 39.5339 48.8833 28.0003C48.8833 16.4668 39.5335 7.11699 28 7.11699C16.4664 7.11699 7.11663 16.4668 7.11663 28.0003C7.11663 39.5339 16.4664 48.8837 28 48.8837ZM28 51.3337C40.8866 51.3337 51.3333 40.887 51.3333 28.0003C51.3333 15.1137 40.8866 4.66699 28 4.66699C15.1133 4.66699 4.66663 15.1137 4.66663 28.0003C4.66663 40.887 15.1133 51.3337 28 51.3337Z"
                fill="#F50000"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M21.2718 39.4094C21.9205 39.6016 22.6022 39.2315 22.7943 38.5828C23.4597 36.3362 25.5403 34.6993 28 34.6993C30.4596 34.6993 32.5402 36.3362 33.2056 38.5828C33.3978 39.2315 34.0794 39.6016 34.7281 39.4094C35.3768 39.2173 35.7469 38.5356 35.5547 37.887C34.5896 34.6285 31.5742 32.2493 28 32.2493C24.4257 32.2493 21.4104 34.6285 20.4452 37.887C20.253 38.5356 20.6232 39.2173 21.2718 39.4094Z"
                fill="#F50000"
            />
        </svg>

    );
};

export default Icon;
