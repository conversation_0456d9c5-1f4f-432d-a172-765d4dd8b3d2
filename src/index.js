import React from "react";
import ReactD<PERSON> from "react-dom";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import "./styles/root.css";
import App from "./components/app";
import { RecoilRoot } from "recoil";

ReactDOM.render(
  <React.StrictMode>
    <RecoilRoot>
      <BrowserRouter>
        <Routes>
          <Route path="/*" element={<App />} />
        </Routes>
      </BrowserRouter>
    </RecoilRoot>
  </React.StrictMode>,
  document.getElementById("app")
);
